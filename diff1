commit d9c70492c9f5279b94ccd0e6853719f23ab2e990
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Wed Jun 18 12:57:25 2025 +0530

    ET-94230: modified the api function to update the customer_gid from c_state

diff --git a/src/zpn/zpn_broker.c b/src/zpn/zpn_broker.c
index c147a358f7..27f031dbe6 100755
--- a/src/zpn/zpn_broker.c
+++ b/src/zpn/zpn_broker.c
@@ -7611,21 +7611,21 @@ void zpn_broker_client_connected_customer_pagination_state_cleanup(struct zpn_br
  *  This func. removes the cstate from customer_gid_entry, and cleans up the hashtable , if needed
  *  Note that, we pass in customer_gid in cases the customer_gid has changed; and we need to remove the entry from old locn.
  */
-void zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t customer_gid)
+int zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t customer_gid)
 {
     if (!c_state) {
         ZPN_LOG(AL_ERROR, "NULL c_state : Error deleting c_state from hash table zpn_broker_cgid_to_cstate_per_thread");
-        return;
+        return ZPATH_RESULT_BAD_ARGUMENT;
     }
 
     if (c_state->customer_gid_entry_incomplete != 0) {
         ZPN_LOG(AL_ERROR, "customer_gid_entry is incomplete for c_state of type: %s , return!", zpn_client_type_string(c_state->client_type));
-        return;
+        return ZPATH_RESULT_BAD_ARGUMENT;
     }
 
     if (0 == c_state->customer_gid) {
         ZPN_LOG(AL_ERROR, "Error deleting c_state from hash table for client type: %s: customer_gid is 0, return!", zpn_client_type_string(c_state->client_type));
-        return;
+        return ZPATH_RESULT_BAD_ARGUMENT;
     }
 
     /* update head of customer_gid list  and counters */
@@ -7635,7 +7635,7 @@ void zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_f
     int thread_id = c_state->conn_thread_id;
     if (thread_id < 0 || thread_id >= FOHH_MAX_THREADS) {
         ZPN_LOG(AL_CRITICAL, "drain_conn: c_state fohh thread is out-of-range in remove cstate from customer gid entry");
-        return;
+        return ZPATH_RESULT_BAD_ARGUMENT;
     }
 
     gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[thread_id],
@@ -7644,7 +7644,7 @@ void zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_f
     if (NULL == gid_client_list) {
         ZPN_LOG(AL_ERROR, "Error deleting c_state from cgid_cstate hash at thread: %d for "
                             "customer %"PRId64" : NULL client list", thread_id, customer_gid);
-        return;
+        return ZPATH_RESULT_NOT_FOUND;
     }
 
     /*
@@ -7694,32 +7694,34 @@ void zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_f
 
         ZPN_FREE(gid_client_list);
     }
+
+    return ZPATH_RESULT_NO_ERROR;
 }
 
 /*
  * zpn_broker_update_cstate_customer_gid_entry
- *  Placing the cstate in the right hashtable in cgid_cstate->customer_gid
+ * Place the cstate in new list after the removal is successful from the old cstate list
  */
 void zpn_broker_update_cstate_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state,
-                                                 int64_t prev_customer_gid)
+                                                 int64_t new_customer_gid)
 {
     if (!c_state)
         return;
 
     /* customer_gid is same implies entry was added correct apriori */
-    if (prev_customer_gid == c_state->customer_gid)
+    if (new_customer_gid == c_state->customer_gid)
         return;
 
-    /* disassociate from old gid. */
-    if (prev_customer_gid) {
-        zpn_broker_remove_cstate_from_customer_gid_entry(c_state, prev_customer_gid);
+    if (!zpn_broker_remove_cstate_from_customer_gid_entry(c_state, 0)) {
+        c_state->customer_gid = new_customer_gid;
+        zpn_broker_add_cstate_to_customer_gid_entry(c_state);
+        c_state->customer_gid_entry_incomplete = 0;
+        ZPN_DEBUG_CLIENT("%s: Successfully updated the customer_gid to %"PRId64"",
+                          c_state->tunnel_id, c_state->customer_gid);
+    } else {
+        ZPN_LOG(AL_ERROR, "%s: could not remove the c_state object from the customer_gid list",
+                           c_state->tunnel_id);
     }
-
-    /* associate with new gid */
-    zpn_broker_add_cstate_to_customer_gid_entry(c_state);
-
-    /* customer gid entry is now complete */
-    c_state->customer_gid_entry_incomplete = 0;
 }
 
 void zpn_broker_add_client(struct zpn_broker_client_fohh_state *c_state)
diff --git a/src/zpn/zpn_broker_client.c b/src/zpn/zpn_broker_client.c
index 05b71f0215..584b07624b 100644
--- a/src/zpn/zpn_broker_client.c
+++ b/src/zpn/zpn_broker_client.c
@@ -11242,14 +11242,15 @@ static int zpn_trusted_client_authenticate_cb(void *argo_cookie_ptr,
         }
     }
 
-    int64_t prev_customer_gid = c_state->customer_gid;
-
     auth = object->base_structure_void;
-    c_state->customer_gid = auth->customer_gid;
     c_state->auth_request_id = auth->id;
 
-    /* update customer_gid_entry stats with the updated customer_gid */
-    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);
+    int64_t new_customer_gid;
+    new_customer_gid = auth->customer_gid;
+    if (new_customer_gid) {
+        /* update customer_gid_entry stats with the updated customer_gid */
+        zpn_broker_update_cstate_customer_gid_entry(c_state, new_customer_gid);
+    }
 
     res = zpn_application_customer_register(c_state->customer_gid, NULL, NULL, 0);
     if (res && (res != ZPN_RESULT_ASYNCHRONOUS)) {
@@ -11428,13 +11429,14 @@ static int zpn_exporter_client_authenticate_internal(struct zpn_broker_client_fo
         return ZPN_RESULT_ERR;
     }
 
-    int64_t prev_customer_gid = c_state->customer_gid;
-
     c_state->auth_request_id = auth->id;
-    c_state->customer_gid = auth->customer_gid;
 
-    /* update customer_gid_entry stats with the updated customer_gid */
-    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);
+    int64_t new_customer_gid;
+    new_customer_gid = auth->customer_gid;
+    if (new_customer_gid) {
+        /* update customer_gid_entry stats with the updated customer_gid */
+        zpn_broker_update_cstate_customer_gid_entry(c_state, new_customer_gid);
+    }
 
     if (auth->public_ip) {
         res = zpn_broker_client_region_check_internal(auth->public_ip, c_state->customer_gid, "exporter");
@@ -11601,12 +11603,12 @@ static int zpn_bi_client_authenticate_internal(struct zpn_broker_client_fohh_sta
         }
     }
 
-    int64_t prev_customer_gid = c_state->customer_gid;
-
-    c_state->customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpn_tlv_peer_get_id(tlv));
-
-    /* update customer_gid_entry stats with the updated customer_gid */
-    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);
+    int64_t new_customer_gid;
+    new_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpn_tlv_peer_get_id(tlv));
+    if (new_customer_gid) {
+        /* update customer_gid_entry stats with the updated customer_gid */
+        zpn_broker_update_cstate_customer_gid_entry(c_state, new_customer_gid);
+    }
 
     if (auth->public_ip) {
         c_state->log.pub_ip = *(auth->public_ip);
@@ -11928,8 +11930,6 @@ static int zpn_ec_client_authenticate_cb(void *argo_cookie_ptr,
     // cache the downstream's peer id for use by other modules especially policy
     c_state->downstream_peer_id = auth->downstream_peer_id;
 
-    int64_t prev_customer_gid = c_state->customer_gid;
-
     auth = object->base_structure_void;
     int64_t new_customer_gid;
     new_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpn_tlv_peer_get_id(tlv));
@@ -11938,9 +11938,8 @@ static int zpn_ec_client_authenticate_cb(void *argo_cookie_ptr,
         return ZPN_RESULT_ERR;
     }
 
-    c_state->customer_gid = new_customer_gid;
     /* update customer_gid_entry stats with the updated customer_gid */
-    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);
+    zpn_broker_update_cstate_customer_gid_entry(c_state, new_customer_gid);
 
     /*
      * For ec we use default scope i.e. customer gid
@@ -12631,12 +12630,14 @@ static int zpn_bc_client_authenticate_cb(void *argo_cookie_ptr,
         }
     }
 
-    int64_t prev_customer_gid = c_state->customer_gid;
+    int64_t new_customer_gid;
 
-    c_state->customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpn_tlv_peer_get_id(tlv));
+    new_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpn_tlv_peer_get_id(tlv));
 
-    /* update customer_gid_entry stats with the updated customer_gid */
-    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);
+    if (new_customer_gid) {
+        /* update customer_gid_entry stats with the updated customer_gid */
+        zpn_broker_update_cstate_customer_gid_entry(c_state, new_customer_gid);
+    }
 
     /*
      * For bc we use default scope i.e. customer gid . EC does the same
@@ -12991,7 +12992,6 @@ static int zpn_ia_client_authenticate_cb(void *argo_cookie_ptr,
         return ZPN_RESULT_NO_ERROR;
     }
 
-    int64_t prev_customer_gid = c_state->customer_gid;
     int64_t new_customer_gid;
 
     if (auth->customer_gid) {
@@ -13014,13 +13014,10 @@ static int zpn_ia_client_authenticate_cb(void *argo_cookie_ptr,
         }
     }
 
-    /* set the customer gid as found above */
-    c_state->customer_gid = new_customer_gid;
-
-    zpn_tlv_peer_set_id(tlv, c_state->customer_gid);
+    zpn_tlv_peer_set_id(tlv, new_customer_gid);
 
     /* update customer_gid_entry stats with the updated customer_gid */
-    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);
+    zpn_broker_update_cstate_customer_gid_entry(c_state, new_customer_gid);
 
     if (auth->cloud_name)
         c_state->zia_cloud_name = ZPN_BCA_STRDUP(auth->cloud_name, strlen(auth->cloud_name));
diff --git a/src/zpn/zpn_broker_private.h b/src/zpn/zpn_broker_private.h
index adf9ea8e1b..a5e857a117 100644
--- a/src/zpn/zpn_broker_private.h
+++ b/src/zpn/zpn_broker_private.h
@@ -1337,8 +1337,9 @@ extern int c_state_get_mtunnel_count(struct zpn_broker_client_fohh_state *c_stat
  *
  */
 void zpn_broker_add_cstate_to_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state);
-void zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t customer_gid);
+int zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t customer_gid);
 void zpn_broker_update_cstate_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t prev_customer_gid);
+void zpn_broker_cstate_update_customer_gid(struct zpn_broker_client_fohh_state *c_state, int64_t new_customer_gid);
 
 void zpn_broker_add_client(struct zpn_broker_client_fohh_state *zc);
 void zpn_broker_remove_client(struct zpn_broker_client_fohh_state *zc);
diff --git a/src/zpn/zpn_vdi/zpn_vdi.c b/src/zpn/zpn_vdi/zpn_vdi.c
index b76e84af4b..3e38f8201b 100644
--- a/src/zpn/zpn_vdi/zpn_vdi.c
+++ b/src/zpn/zpn_vdi/zpn_vdi.c
@@ -185,10 +185,9 @@ int zpn_vdi_client_authenticate_cb(void *argo_cookie_ptr, void *argo_structure_c
     // cache the downstream's peer id for use by other modules especially policy
     c_state->downstream_peer_id = auth->downstream_peer_id;
 
-    int64_t prev_customer_gid = c_state->customer_gid;
-    int64_t new_customer_gid;
-
     auth = object->base_structure_void;
+
+    int64_t new_customer_gid;
     new_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpn_tlv_peer_get_id(tlv));
     if (!new_customer_gid) {
         ZPN_LOG(AL_ERROR, "%s: No peer gid from certificate verification", zpn_tlv_description(tlv));
@@ -197,9 +196,8 @@ int zpn_vdi_client_authenticate_cb(void *argo_cookie_ptr, void *argo_structure_c
         goto AUTH_FAIL;
     }
 
-    c_state->customer_gid = new_customer_gid;
     /* update customer_gid_entry stats with the updated customer_gid */
-    zpn_broker_update_cstate_customer_gid_entry(c_state, prev_customer_gid);
+    zpn_broker_update_cstate_customer_gid_entry(c_state, new_customer_gid);
 
     /*
      * For vdi we use default scope i.e. customer gid
